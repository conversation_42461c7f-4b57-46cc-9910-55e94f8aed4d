import React, { useState, useRef } from 'react';
import { Shield, Eye, FileText, Code, Search, Download, Upload, Play, Pause, CheckCircle, AlertTriangle, Info } from 'lucide-react';

const SentinelTraceUI = () => {
  const [activeTab, setActiveTab] = useState('analyze');
  const [codeInput, setCodeInput] = useState('');
  const [analysisType, setAnalysisType] = useState('comprehensive');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [showThoughtProcess, setShowThoughtProcess] = useState(true);
  const fileInputRef = useRef(null);

  const phases = [
    'Deconstruction & Context Assimilation',
    'Hypothesis Generation & Threat Modeling',
    'Iterative Deep Analysis & Evidence Gathering',
    'Self-Critique & Alternative Explanation',
    'Impact & Exploit Chain Analysis',
    'Remediation Strategy Formulation',
    'Synthesis & Final Report Generation'
  ];

  const mockThoughtProcess = `
# Phase 1: Deconstruction & Context Assimilation

**Acknowledging Request:**
User has submitted Flask web application code for security analysis.

**Context Summary:**
- Language: Python 3.x
- Framework: Flask web framework
- Components: User authentication, file upload, database operations
- Goal: Identify OWASP Top 10 2021 vulnerabilities

**Knowns:**
- Flask application with SQLAlchemy ORM
- User input handling via request.form and request.files
- Database queries using both ORM and raw SQL
- File upload functionality present
- Session management implemented

**Unknowns/Assumptions:**
- Exact Flask version not specified; assuming recent stable version
- Database schema unknown; assuming contains sensitive user data
- Production deployment configuration unknown
- Input validation middleware not clearly visible

---

# Phase 2: Hypothesis Generation & Threat Modeling

**Initial Hypotheses:**

**Hypothesis A:** SQL Injection (A03: Injection)
- Line 47: raw SQL query construction using string formatting
- User input from request.form['username'] concatenated directly

**Hypothesis B:** Path Traversal (A01: Broken Access Control)
- upload_file() function constructs file paths from user input
- No apparent path sanitization visible

**Hypothesis C:** Insecure Direct Object Reference (A01: Broken Access Control)
- get_user_data() accepts user_id parameter without authorization check

**Hypothesis D:** Hardcoded Secrets (A02: Cryptographic Failures)
- SECRET_KEY appears to be hardcoded in configuration

---

# Phase 3: Iterative Deep Analysis & Evidence Gathering

**Investigating Hypothesis A - SQL Injection:**

Evidence found at line 47:
\`\`\`python
query = f"SELECT * FROM users WHERE username = '{username}'"
cursor.execute(query)
\`\`\`

**Critical Finding:** Direct string interpolation of user input into SQL query.
Data flow: request.form['username'] → username variable → SQL string

**Investigating Hypothesis B - Path Traversal:**

Evidence found at line 82:
\`\`\`python
filename = request.files['file'].filename
filepath = os.path.join(UPLOAD_FOLDER, filename)
\`\`\`

**Critical Finding:** No path sanitization or validation of filename.
Attack vector: "../../../etc/passwd" could escape upload directory.

**Investigating Hypothesis C - IDOR:**

Evidence found at line 103:
\`\`\`python
def get_user_data(user_id):
    return User.query.get(user_id)
\`\`\`

**Moderate Finding:** No authorization check if current user can access requested user_id.

**Investigating Hypothesis D - Hardcoded Secrets:**

Evidence found at line 12:
\`\`\`python
app.config['SECRET_KEY'] = 'dev-secret-key-12345'
\`\`\`

**Critical Finding:** Production secret key hardcoded in source code.

---

# Phase 4: Self-Critique & Alternative Explanation

**Challenging Hypothesis A (SQL Injection):**
- Is there input validation elsewhere? **No validation middleware detected**
- Could ORM provide protection? **Raw SQL bypasses ORM protections**
- Is this code path reachable? **Yes, login endpoint uses this function**
**Status: VALIDATED**

**Challenging Hypothesis B (Path Traversal):**
- Does Flask provide automatic protection? **No built-in path traversal protection**
- Are uploads restricted to specific directory? **os.path.join() vulnerable to ../ sequences**
- Could file permissions limit impact? **Depends on deployment, assuming standard permissions**
**Status: VALIDATED**

**Challenging Hypothesis C (IDOR):**
- Is authorization checked at route level? **No @requires_auth decorator visible**
- Could session management prevent this? **Session exists but no ownership validation**
**Status: VALIDATED**

**Challenging Hypothesis D (Hardcoded Secrets):**
- Could this be development-only code? **Represents security anti-pattern regardless**
- Are environment variables used elsewhere? **No evidence of proper secret management**
**Status: VALIDATED**

---

# Phase 5: Impact & Exploit Chain Analysis

**SQL Injection Impact:**
- Technical: Complete database compromise, arbitrary SQL execution
- Business: Customer data breach, regulatory violations, reputational damage
- Exploit Chain: SQL injection → database admin → lateral movement to file system

**Path Traversal Impact:**
- Technical: Arbitrary file read/write, potential code execution
- Business: Intellectual property theft, system compromise
- Exploit Chain: File upload → path traversal → overwrite critical system files

**Combined Attack Scenario:**
1. Attacker exploits SQL injection to enumerate admin users
2. Uses path traversal to overwrite authentication module
3. Gains administrative access to entire application

---

# Phase 6: Remediation Strategy Formulation

**SQL Injection Remediation:**
Primary: Replace string formatting with parameterized queries
\`\`\`python
cursor.execute("SELECT * FROM users WHERE username = %s", (username,))
\`\`\`

**Path Traversal Remediation:**
Primary: Implement secure filename validation
\`\`\`python
import os
filename = secure_filename(request.files['file'].filename)
filepath = os.path.join(UPLOAD_FOLDER, filename)
if not filepath.startswith(UPLOAD_FOLDER):
    abort(400)
\`\`\`

**IDOR Remediation:**
Primary: Add authorization checks
\`\`\`python
@requires_auth
def get_user_data(user_id):
    if current_user.id != user_id and not current_user.is_admin:
        abort(403)
    return User.query.get(user_id)
\`\`\`

---

# Phase 7: Synthesis & Final Report Generation

Compiling 4 validated vulnerabilities into structured JSON report...
- 3 Critical severity findings
- 1 Medium severity finding
- All findings supported by concrete evidence
- Remediation strategies provided with code examples
`;

  const mockVulnerabilityReport = {
    "executive_summary": "Analysis identified 4 critical security vulnerabilities in the Flask application, including SQL injection, path traversal, and insecure direct object references. Immediate remediation required to prevent data breach and system compromise.",
    "vulnerabilities": [
      {
        "id": "VULN-001",
        "title": "SQL Injection in User Authentication",
        "owasp_category": "A03: Injection",
        "cvss_v3_score": 9.8,
        "cvss_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
        "description": "Direct string interpolation of user input into SQL query without parameterization allows for SQL injection attacks.",
        "proof_of_concept": "POST /login with username=admin'; DROP TABLE users; -- bypasses authentication and could delete user data.",
        "technical_impact": "Complete database compromise, arbitrary SQL execution, data exfiltration",
        "business_impact": "Customer data breach, regulatory violations (GDPR/CCPA), reputational damage, potential lawsuits",
        "remediation_strategy": "Replace string formatting with parameterized queries using cursor.execute() with parameter binding.",
        "remediation_code_example": "cursor.execute('SELECT * FROM users WHERE username = %s', (username,))"
      },
      {
        "id": "VULN-002",
        "title": "Path Traversal in File Upload",
        "owasp_category": "A01: Broken Access Control",
        "cvss_v3_score": 8.8,
        "cvss_vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:H",
        "description": "File upload functionality lacks proper path validation, allowing directory traversal attacks.",
        "proof_of_concept": "Upload file with name '../../../etc/passwd' to read system files or overwrite critical application files.",
        "technical_impact": "Arbitrary file read/write, potential remote code execution, system file manipulation",
        "business_impact": "Intellectual property theft, system compromise, service disruption",
        "remediation_strategy": "Implement secure filename validation and restrict file operations to designated upload directory.",
        "remediation_code_example": "filename = secure_filename(request.files['file'].filename)\\nif not os.path.abspath(filepath).startswith(UPLOAD_FOLDER): abort(400)"
      },
      {
        "id": "VULN-003",
        "title": "Insecure Direct Object Reference",
        "owasp_category": "A01: Broken Access Control",
        "cvss_v3_score": 6.5,
        "cvss_vector": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N",
        "description": "User data access function lacks authorization checks, allowing users to access other users' data.",
        "proof_of_concept": "Authenticated user can access /user/123/data to view any user's private information by changing the ID parameter.",
        "technical_impact": "Unauthorized data access, privacy violations, horizontal privilege escalation",
        "business_impact": "Privacy law violations, customer trust loss, potential regulatory fines",
        "remediation_strategy": "Implement proper authorization checks to verify user ownership or admin privileges.",
        "remediation_code_example": "if current_user.id != user_id and not current_user.is_admin:\\n    abort(403)"
      },
      {
        "id": "VULN-004",
        "title": "Hardcoded Secret Key",
        "owasp_category": "A02: Cryptographic Failures",
        "cvss_v3_score": 7.5,
        "cvss_vector": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:N/A:N",
        "description": "Application uses hardcoded secret key in source code, compromising session security.",
        "proof_of_concept": "Secret key 'dev-secret-key-12345' visible in source code allows session token forgery and authentication bypass.",
        "technical_impact": "Session hijacking, authentication bypass, user impersonation",
        "business_impact": "Account takeover, unauthorized access to user data, compliance violations",
        "remediation_strategy": "Use environment variables or secure key management system for secret keys.",
        "remediation_code_example": "app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY')"
      }
    ]
  };

  const handleAnalyze = () => {
    setIsAnalyzing(true);
    setAnalysisComplete(false);

    // Simulate analysis process
    setTimeout(() => {
      setIsAnalyzing(false);
      setAnalysisComplete(true);
    }, 5000);
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* Header */}
      <header className="border-b border-slate-700 bg-slate-900/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Shield className="w-8 h-8 text-cyan-400" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                  Sentinel Trace
                </h1>
                <p className="text-sm text-slate-400">Metacognitive Security Analyst AI</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-slate-300">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>System Active</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-slate-800/30 border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex space-x-8">
            {[
              { id: 'analyze', label: 'Code Analysis', icon: Search },
              { id: 'reports', label: 'Reports', icon: FileText },
              { id: 'settings', label: 'Configuration', icon: Code }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-3 border-b-2 transition-colors ${activeTab === tab.id
                    ? 'border-cyan-400 text-cyan-400'
                    : 'border-transparent text-slate-400 hover:text-slate-200'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {activeTab === 'analyze' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Input Panel */}
            <div className="space-y-6">
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700">
                <h2 className="text-lg font-semibold mb-4 flex items-center">
                  <Code className="w-5 h-5 mr-2 text-cyan-400" />
                  Code Input
                </h2>

                <div className="space-y-4">
                  <div className="flex space-x-3">
                    <button
                      onClick={handleFileUpload}
                      className="flex items-center space-x-2 px-4 py-2 bg-slate-700 hover:bg-slate-600 rounded-lg transition-colors"
                    >
                      <Upload className="w-4 h-4" />
                      <span>Upload Files</span>
                    </button>
                    <select
                      value={analysisType}
                      onChange={(e) => setAnalysisType(e.target.value)}
                      className="px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg focus:border-cyan-400 focus:outline-none"
                    >
                      <option value="comprehensive">Comprehensive Analysis</option>
                      <option value="quick">Quick Scan</option>
                      <option value="targeted">Targeted Assessment</option>
                    </select>
                  </div>

                  <textarea
                    value={codeInput}
                    onChange={(e) => setCodeInput(e.target.value)}
                    placeholder="Paste your code here for analysis..."
                    className="w-full h-64 p-4 bg-slate-900 border border-slate-600 rounded-lg font-mono text-sm focus:border-cyan-400 focus:outline-none resize-none"
                  />

                  <button
                    onClick={handleAnalyze}
                    disabled={!codeInput.trim() || isAnalyzing}
                    className="w-full flex items-center justify-center space-x-2 py-3 bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-500 hover:to-blue-500 disabled:from-slate-600 disabled:to-slate-600 rounded-lg font-medium transition-all"
                  >
                    {isAnalyzing ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>Analyzing...</span>
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4" />
                        <span>Start Deep Analysis</span>
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Analysis Progress */}
              {(isAnalyzing || analysisComplete) && (
                <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700">
                  <h3 className="text-lg font-semibold mb-4 flex items-center">
                    <Eye className="w-5 h-5 mr-2 text-cyan-400" />
                    Deep Reasoning Protocol
                  </h3>
                  <div className="space-y-3">
                    {phases.map((phase, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                          (isAnalyzing && index === 2) ? 'bg-cyan-500 animate-pulse' :
                          (analysisComplete || (!isAnalyzing && index < 3)) ? 'bg-green-500' :
                          'bg-slate-600'
                        }`}>
                          {analysisComplete || (!isAnalyzing && index < 3) ? (
                            <CheckCircle className="w-3 h-3" />
                          ) : (
                            index + 1
                          )}
                        </div>
                        <span className={`text-sm ${
                          analysisComplete || (!isAnalyzing && index < 3) ? 'text-green-400' :
                          (isAnalyzing && index === 2) ? 'text-cyan-400' :
                          'text-slate-400'
                        }`}>
                          {phase}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Results Panel */}
            <div className="space-y-6">
              {analysisComplete && (
                <>
                  <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold flex items-center">
                        <FileText className="w-5 h-5 mr-2 text-cyan-400" />
                        Analysis Results
                      </h3>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setShowThoughtProcess(!showThoughtProcess)}
                          className={`px-3 py-1 rounded-lg text-sm transition-colors ${showThoughtProcess ? 'bg-cyan-600 text-white' : 'bg-slate-700 text-slate-300'}`}
                        >
                          Thought Process
                        </button>
                        <button className="flex items-center space-x-1 px-3 py-1 bg-slate-700 hover:bg-slate-600 rounded-lg text-sm transition-colors">
                          <Download className="w-3 h-3" />
                          <span>Export</span>
                        </button>
                      </div>
                    </div>

                    {/* Vulnerability Summary */}
                    <div className="grid grid-cols-4 gap-4 mb-6">
                      <div className="bg-red-900/30 border border-red-700 rounded-lg p-3 text-center">
                        <div className="text-2xl font-bold text-red-400">1</div>
                        <div className="text-xs text-red-300">Critical</div>
                      </div>
                      <div className="bg-orange-900/30 border border-orange-700 rounded-lg p-3 text-center">
                        <div className="text-2xl font-bold text-orange-400">2</div>
                        <div className="text-xs text-orange-300">High</div>
                      </div>
                      <div className="bg-yellow-900/30 border border-yellow-700 rounded-lg p-3 text-center">
                        <div className="text-2xl font-bold text-yellow-400">3</div>
                        <div className="text-xs text-yellow-300">Medium</div>
                      </div>
                      <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-3 text-center">
                        <div className="text-2xl font-bold text-blue-400">1</div>
                        <div className="text-xs text-blue-300">Low</div>
                      </div>
                    </div>

                    {showThoughtProcess ? (
                      <div className="bg-slate-900 rounded-lg p-4 font-mono text-sm max-h-96 overflow-y-auto">
                        <pre className="whitespace-pre-wrap text-slate-300">{mockThoughtProcess}</pre>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <AlertTriangle className="w-5 h-5 text-red-400" />
                              <span className="font-semibold text-red-400">SQL Injection in Admin Panel</span>
                            </div>
                            <span className="px-2 py-1 bg-red-600 text-white text-xs rounded-full">CRITICAL</span>
                          </div>
                          <p className="text-sm text-slate-300 mb-2">
                            Dynamic SQL query construction allows for SQL injection attacks
                          </p>
                          <div className="text-xs text-slate-400">
                            <span className="font-mono">src/admin/queries.js:47</span> • CWE-89
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {activeTab === 'reports' && (
          <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700">
            <h2 className="text-xl font-semibold mb-6">Analysis Reports</h2>
            <div className="text-center py-12 text-slate-400">
              <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No reports available yet. Start an analysis to generate your first report.</p>
            </div>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700">
            <h2 className="text-xl font-semibold mb-6">Configuration</h2>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">Analysis Depth</label>
                <select className="w-full px-4 py-2 bg-slate-700 border border-slate-600 rounded-lg focus:border-cyan-400 focus:outline-none">
                  <option>Standard (7 phases)</option>
                  <option>Extended (10 phases)</option>
                  <option>Quick (5 phases)</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Output Format</label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" defaultChecked className="mr-2" />
                    <span>Include Thought Process Log</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" defaultChecked className="mr-2" />
                    <span>Generate JSON Schema Report</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".js,.py,.java,.cpp,.c,.php,.rb,.go,.rs"
        className="hidden"
        onChange={() => {}}
      />
    </div>
  );
};

export default SentinelTraceUI;
