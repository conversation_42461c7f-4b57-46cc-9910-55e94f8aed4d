# Sentinel Trace Technology Selection Plan

## 1. Define JSON Schema for Final Vulnerability Report

This section defines a comprehensive JSON schema that captures all necessary information for a vulnerability report, incorporating the user stories for "Detailed Thought Process," "Code Analysis Summary," "Remediation Guidance," and "Progress Tracking."

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Sentinel Trace Vulnerability Report",
  "description": "Schema for the final vulnerability report generated by Sentinel Trace.",
  "type": "object",
  "properties": {
    "reportId": {
      "type": "string",
      "description": "Unique identifier for this specific vulnerability report."
    },
    "timestamp": {
      "type": "string",
      "format": "date-time",
      "description": "UTC timestamp when the report was generated."
    },
    "codebaseInfo": {
      "type": "object",
      "description": "Information about the analyzed codebase.",
      "properties": {
        "language": {
          "type": "string",
          "description": "Primary programming language of the analyzed code (e.g., 'Python', 'JavaScript', 'Java')."
        },
        "framework": {
          "type": "string",
          "description": "Framework detected in the analyzed code (e.g., 'Django', 'React', 'Spring Boot'). Can be null if no specific framework."
        },
        "environment": {
          "type": "string",
          "description": "Details about the intended deployment environment (e.g., 'Linux', 'Docker', 'Node.js v18')."
        },
        "commitHash": {
          "type": "string",
          "description": "Git commit hash of the analyzed codebase, if applicable."
        }
      },
      "required": ["language"]
    },
    "vulnerabilities": {
      "type": "array",
      "description": "A list of identified security vulnerabilities.",
      "items": {
        "type": "object",
        "properties": {
          "vulnerabilityId": {
            "type": "string",
            "description": "A unique identifier for the vulnerability instance within this report (e.g., 'VULN-001')."
          },
          "cweId": {
            "type": "string",
            "description": "Common Weakness Enumeration (CWE) ID, if applicable (e.g., 'CWE-79')."
          },
          "name": {
            "type": "string",
            "description": "Concise name of the vulnerability (e.g., 'Cross-Site Scripting (XSS)')."
          },
          "description": {
            "type": "string",
            "description": "A detailed explanation of the vulnerability, its nature, and potential impact."
          },
          "severity": {
            "type": "string",
            "enum": ["Critical", "High", "Medium", "Low", "Informational"],
            "description": "The severity level of the vulnerability."
          },
          "confidence": {
            "type": "string",
            "enum": ["High", "Medium", "Low"],
            "description": "The confidence level in the detection of this vulnerability."
          },
          "affectedLocations": {
            "type": "array",
            "description": "Specific locations in the code where the vulnerability was found.",
            "items": {
              "type": "object",
              "properties": {
                "file": {
                  "type": "string",
                  "description": "Path to the affected file relative to the codebase root."
                },
                "startLine": {
                  "type": "integer",
                  "minimum": 1,
                  "description": "The starting line number of the vulnerable code snippet."
                },
                "endLine": {
                  "type": "integer",
                  "minimum": 1,
                  "description": "The ending line number of the vulnerable code snippet."
                },
                "codeSnippet": {
                  "type": "string",
                  "description": "The exact code snippet identified as vulnerable."
                }
              },
              "required": ["file", "startLine", "endLine", "codeSnippet"]
            }
          },
          "analysisSummary": {
            "type": "string",
            "description": "A concise summary of how the vulnerability was identified and its immediate implications. (User Story 3)"
          },
          "detailedThoughtProcess": {
            "type": "string",
            "description": "The detailed reasoning and steps taken by the LLM during the analysis to identify this vulnerability. (User Story 2)"
          },
          "remediationGuidance": {
            "type": "string",
            "description": "Actionable steps and best practices, potentially with code examples, to fix the vulnerability. (User Story 4)"
          },
          "references": {
            "type": "array",
            "description": "External links or resources for more information about the vulnerability (e.g., OWASP, CVE, official documentation).",
            "items": {
              "type": "string",
              "format": "uri"
            }
          },
          "status": {
            "type": "string",
            "enum": ["Open", "Resolved", "False Positive", "Acknowledged"],
            "default": "Open",
            "description": "Current status of the vulnerability for progress tracking. (User Story 5)"
          },
          "resolutionNotes": {
            "type": "string",
            "description": "Notes on how the vulnerability was resolved or why it was marked as false positive."
          },
          "resolvedBy": {
            "type": "string",
            "description": "Identifier of the user or system that resolved the vulnerability."
          },
          "resolvedAt": {
            "type": "string",
            "format": "date-time",
            "description": "Timestamp when the vulnerability was resolved."
          }
        },
        "required": [
          "vulnerabilityId",
          "name",
          "description",
          "severity",
          "confidence",
          "affectedLocations",
          "analysisSummary",
          "detailedThoughtProcess",
          "remediationGuidance",
          "status"
        ]
      },
      "minItems": 0
    },
    "overallSummary": {
      "type": "object",
      "description": "Summary statistics for the entire report.",
      "properties": {
        "totalVulnerabilities": {
          "type": "integer",
          "minimum": 0,
          "description": "Total number of vulnerabilities identified in the report."
        },
        "severityBreakdown": {
          "type": "object",
          "description": "Count of vulnerabilities by severity level.",
          "properties": {
            "Critical": { "type": "integer", "minimum": 0 },
            "High": { "type": "integer", "minimum": 0 },
            "Medium": { "type": "integer", "minimum": 0 },
            "Low": { "type": "integer", "minimum": 0 },
            "Informational": { "type": "integer", "minimum": 0 }
          },
          "required": ["Critical", "High", "Medium", "Low", "Informational"]
        },
        "progressTracking": {
          "type": "object",
          "description": "Overall progress tracking for the report. (User Story 5)",
          "properties": {
            "overallStatus": {
              "type": "string",
              "enum": ["Pending Analysis", "Analysis Complete", "Remediation In Progress", "All Resolved"],
              "description": "High-level status of the entire report's remediation."
            },
            "resolvedCount": {
              "type": "integer",
              "minimum": 0,
              "description": "Number of vulnerabilities marked as 'Resolved'."
            },
            "openCount": {
              "type": "integer",
              "minimum": 0,
              "description": "Number of vulnerabilities still 'Open'."
            },
            "lastProgressUpdate": {
              "type": "string",
              "format": "date-time",
              "description": "Timestamp of the last update to the overall progress."
            }
          },
          "required": ["overallStatus", "resolvedCount", "openCount"]
        }
      },
      "required": ["totalVulnerabilities", "severityBreakdown", "progressTracking"]
    }
  },
  "required": ["reportId", "timestamp", "codebaseInfo", "vulnerabilities", "overallSummary"]
}
```

## 2. Technology Recommendations

Based on the user stories, input/output specifications, and performance goals, here are the technology recommendations:

*   **Backend Framework (Python): FastAPI**
    *   **Reasoning:** FastAPI is an excellent choice due to its high performance (comparable to Node.js and Go), built-in support for asynchronous operations, and automatic generation of OpenAPI (Swagger) documentation. Its performance is crucial for meeting the "Analysis Response Time: ≤ 5 minutes" and "Remediation Guidance Response Time: ≤ 1 minute" goals, especially when dealing with potentially long-running LLM calls. It's also very developer-friendly, making it easy to define request/response schemas using Pydantic, which aligns well with structuring LLM inputs/outputs. Flask is also a good option, but FastAPI's async capabilities and performance edge make it more suitable for a high-throughput, LLM-centric application.

*   **Frontend Framework (Web): React with Next.js**
    *   **Reasoning:** React provides a robust component-based architecture for building complex UIs, which is ideal for a detailed security dashboard. Next.js, built on React, offers crucial features like Server-Side Rendering (SSR) or Static Site Generation (SSG), which can significantly improve the "Dashboard Loading Time: ≤ 2 seconds" by delivering pre-rendered HTML to the client. It also simplifies routing, API routes (if needed for small client-side data fetching), and overall project structure, providing a more complete solution than Create React App for a production-ready application.

*   **Dashboard UI Library (React): shadcn/ui with Tailwind CSS**
    *   **Reasoning:** `shadcn/ui` provides a collection of re-usable components that are built on top of Radix UI and styled with Tailwind CSS. This combination offers immense flexibility and customization. Tailwind CSS allows for rapid UI development and ensures a consistent design system. Unlike dedicated dashboard templates, `shadcn/ui` gives full control over the components, allowing for a highly tailored and performant dashboard that can meet the "Dashboard Loading Time: ≤ 2 seconds" goal without unnecessary bloat. It also integrates seamlessly with Next.js.

*   **LLM Orchestration/Helpers (Python): ollama Python client library, Pydantic**
    *   **Reasoning:**
        *   **ollama Python client library:** This is essential for interacting with local LLMs (like those run via Ollama or LM Studio, which often provides an OpenAI-compatible API). It allows for sending prompts, receiving responses, and managing LLM interactions directly from the Python backend.
        *   **Pydantic:** Pydantic is critical for defining data models and validating data, especially for LLM inputs and outputs. It allows for robust schema definition (like the JSON schema defined above) and ensures that the LLM's responses conform to expected structures. This is vital for reliable parsing of analysis summaries, remediation guidance, and detailed thought processes, making the LLM's output easily consumable by the application. It also helps in structuring prompts to guide the LLM to produce specific JSON formats. The `lmstudio Python SDK` would be an alternative if LM Studio's specific features are preferred over a generic OpenAI-compatible API, but the `ollama` client library is generally more versatile for local LLM interactions.

## 3. System Architecture Overview

```mermaid
graph TD
    A[User] -->|1. Upload Code & Context| B(Frontend Application - React/Next.js)
    B -->|2. API Request (Code Analysis)| C(Backend API - FastAPI)
    C -->|3. Orchestrate LLM Calls| D(LLM Orchestration/Helpers - Pydantic, Ollama Client)
    D -->|4. Interact with Local LLM| E(Local LLM - Ollama/LM Studio)
    E -->|5. Analysis Results (JSON)| D
    D -->|6. Validate & Structure Data| C
    C -->|7. Store Vulnerability Report| F(Database)
    C -->|8. Return Report ID & Summary| B
    B -->|9. Display Dashboard & Progress| G(Dashboard UI - shadcn/ui, Tailwind CSS)
    G -->|10. Fetch Reports/Progress| C