# Tech Context: Sentinel Trace

**Primary Technologies:**

The following core technologies will be utilized in the development of Sentinel Trace:

*   **Backend Framework:** FastAPI (Python)
*   **Frontend Framework:** React
*   **UI Styling:** Tailwind CSS
*   **LLM Interaction:** Ollama / LM Studio (for local LLM API servers)
*   **Data Validation:** Pydantic (for robust schema validation, especially with LLM outputs)

**Development Setup:**

*   **Local API Servers for LLMs:** The system is designed to interact with locally running LLM API servers (e.g., Ollama, LM Studio) to facilitate development and ensure data privacy.