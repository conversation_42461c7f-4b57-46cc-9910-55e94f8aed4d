# System Patterns: Sentinel Trace

**High-Level System Architecture:**

Sentinel Trace is designed with a clear separation of concerns, utilizing a modern web application architecture:

*   **Backend:** Developed with FastAPI (Python) for robust API services, LLM orchestration, and data processing.
*   **Frontend:** Built with React for a dynamic and responsive user interface.
*   **LLM Integration:** Utilizes local LLM API servers (Ollama/LM Studio) for core AI reasoning capabilities.
*   **RAG Database:** Incorporates a Retrieval-Augmented Generation (RAG) database to provide LLMs with relevant security context.

**Key Technical Decisions:**

*   **Deep Reasoning Protocol:** A multi-phase, iterative protocol guiding the LLM through a structured vulnerability analysis process, ensuring comprehensive and transparent results.
*   **Structured Output:** Strict enforcement of JSON schema for LLM outputs to ensure data consistency and facilitate integration with the frontend.