<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentinel Trace - Multi-Agent Development Hub</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: #e2e8f0;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #06b6d4, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #94a3b8;
            margin-bottom: 20px;
        }

        .agent-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .agent-login {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 12px;
            padding: 20px;
        }

        .agent-status {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 12px;
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #cbd5e1;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(148, 163, 184, 0.3);
            border-radius: 8px;
            color: #e2e8f0;
            font-size: 1rem;
        }

        .form-input:focus {
            outline: none;
            border-color: #06b6d4;
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #06b6d4, #3b82f6);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(6, 182, 212, 0.3);
        }

        .btn-secondary {
            background: rgba(148, 163, 184, 0.2);
            color: #e2e8f0;
            border: 1px solid rgba(148, 163, 184, 0.3);
        }

        .active-agents {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .agent-badge {
            background: linear-gradient(135deg, #06b6d4, #3b82f6);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .roadmap-container {
            display: grid;
            gap: 25px;
        }

        .phase {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 16px;
            padding: 25px;
            transition: all 0.3s ease;
        }

        .phase:hover {
            border-color: #06b6d4;
            box-shadow: 0 10px 30px rgba(6, 182, 212, 0.1);
        }

        .phase-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .phase-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #06b6d4;
        }

        .phase-timeline {
            background: rgba(148, 163, 184, 0.2);
            color: #94a3b8;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 0.9rem;
        }

        .phase-progress {
            width: 100%;
            height: 8px;
            background: rgba(148, 163, 184, 0.2);
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #06b6d4, #3b82f6);
            transition: width 0.5s ease;
        }

        .tasks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 15px;
        }

        .task-card {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .task-card:hover {
            border-color: #06b6d4;
            transform: translateY(-2px);
        }

        .task-card.assigned {
            border-color: #06b6d4;
            background: rgba(6, 182, 212, 0.1);
        }

        .task-card.completed {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .task-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #e2e8f0;
            flex: 1;
        }

        .task-priority {
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .priority-high {
            background: rgba(239, 68, 68, 0.2);
            color: #fca5a5;
        }

        .priority-medium {
            background: rgba(245, 158, 11, 0.2);
            color: #fcd34d;
        }

        .priority-low {
            background: rgba(148, 163, 184, 0.2);
            color: #94a3b8;
        }

        .task-description {
            color: #94a3b8;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .task-assignment {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(148, 163, 184, 0.2);
        }

        .assigned-agent {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            color: #06b6d4;
            font-weight: 500;
        }

        .progress-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .progress-bar {
            width: 60px;
            height: 6px;
            background: rgba(148, 163, 184, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #06b6d4, #3b82f6);
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.8rem;
            color: #94a3b8;
            font-weight: 500;
        }

        .task-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #06b6d4;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #94a3b8;
            font-size: 0.9rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 16px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #e2e8f0;
        }

        .close-btn {
            background: none;
            border: none;
            color: #94a3b8;
            font-size: 1.5rem;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛡️ Sentinel Trace</div>
            <div class="subtitle">Multi-Agent Development Hub - Metacognitive Security Analyst AI</div>
        </div>

        <!-- Removed AI Agent Prompt Section -->

        <!-- Agent Controls -->
        <div class="agent-controls">
            <div class="agent-login">
                <h3 style="margin-bottom: 15px; color: #e2e8f0;">Agent Check-In</h3>
                <div class="form-group">
                    <label class="form-label" for="agentName">Agent Name</label>
                    <input type="text" class="form-input" id="agentName" placeholder="Enter your AI agent name">
                </div>
                <div class="form-group">
                    <label class="form-label" for="agentSpecialization">Specialization</label>
                    <select class="form-input" id="agentSpecialization">
                        <option value="">Select specialization</option>
                        <option value="Backend Development">Backend Development</option>
                        <option value="Frontend Development">Frontend Development</option>
                        <option value="AI/ML Engineering">AI/ML Engineering</option>
                        <option value="Security Analysis">Security Analysis</option>
                        <option value="DevOps">DevOps</option>
                        <option value="Full Stack">Full Stack</option>
                    </select>
                </div>
                <button class="btn btn-primary" onclick="checkInAgent()">Check In</button>
            </div>

            <div class="agent-status">
                <h3 style="margin-bottom: 15px; color: #e2e8f0;">Active Agents</h3>
                <div id="activeAgentsList">
                    <p style="color: #94a3b8;">No agents currently active</p>
                </div>
            </div>
        </div>

        <!-- Project Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalTasks">0</div>
                <div class="stat-label">Total Tasks</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completedTasks">0</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="inProgressTasks">0</div>
                <div class="stat-label">In Progress</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeAgents">0</div>
                <div class="stat-label">Active Agents</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="overallProgress">0%</div>
                <div class="stat-label">Overall Progress</div>
            </div>
        </div>

        <!-- Roadmap Phases -->
        <div class="roadmap-container">
            <!-- Phase 1: Foundation & Core Architecture -->
            <div class="phase">
                <div class="phase-header">
                    <div class="phase-title">Phase 1: Foundation & Core Architecture</div>
                    <div class="phase-timeline">Weeks 1-4</div>
                </div>
                <div class="phase-progress">
                    <div class="progress-fill" style="width: 25%"></div>
                </div>
                
                <div class="tasks-grid" id="phase-1-tasks"></div>
            </div>

            <!-- Phase 2: User Interface Development -->
            <div class="phase">
                <div class="phase-header">
                    <div class="phase-title">Phase 2: User Interface Development</div>
                    <div class="phase-timeline">Weeks 5-8</div>
                </div>
                <div class="phase-progress">
                    <div class="progress-fill" style="width: 15%"></div>
                </div>
                
                <div class="tasks-grid" id="phase-2-tasks"></div>
            </div>

            <!-- Phase 3: Advanced Features -->
            <div class="phase">
                <div class="phase-header">
                    <div class="phase-title">Phase 3: Advanced Features</div>
                    <div class="phase-timeline">Weeks 9-12</div>
                </div>
                <div class="phase-progress">
                    <div class="progress-fill" style="width: 5%"></div>
                </div>
                
                <div class="tasks-grid" id="phase-3-tasks"></div>
            </div>
        </div>
    </div>

    <!-- Task Detail Modal -->
    <div class="modal" id="taskModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTaskTitle">Task Details</div>
                <button class="close-btn" onclick="closeTaskModal()">&times;</button>
            </div>
            <div id="modalTaskContent">
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <div id="modalTaskDescription" style="color: #94a3b8; margin-bottom: 15px;"></div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="modalAssignAgent">Assign to Agent</label>
                    <select class="form-input" id="modalAssignAgent">
                        <option value="">Select agent</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label" for="modalTaskProgress">Progress</label>
                    <input type="range" class="form-input" id="modalTaskProgress" min="0" max="100" value="0" oninput="document.getElementById('modalTaskProgressText').textContent = this.value + '%';">
                    <span id="modalTaskProgressText">0%</span>
                </div>
            </div>
            <div class="task-actions">
                <button class="btn btn-primary" onclick="saveTaskChanges()">Save Changes</button>
                <button class="btn btn-secondary" onclick="closeTaskModal()">Cancel</button>
            </div>
        </div>
    </div>

    <script>
        let activeAgents = [];
        let currentTaskId = null;

        const tasks = [
            // Phase 1 Tasks
            { id: 'task-1', title: 'Deep Reasoning Protocol Implementation', description: 'Implement the core 7-phase analytical methodology with metacognitive self-critique capabilities', priority: 'High', phase: 1, assignedAgent: 'Agent-Alpha', specialization: 'AI/ML Engineering', progress: 65, status: 'assigned' },
            { id: 'task-2', title: 'API Framework Setup', description: 'Build RESTful endpoints for analysis requests, progress tracking, and result retrieval', priority: 'High', phase: 1, assignedAgent: '', specialization: '', progress: 0, status: 'unassigned' },
            { id: 'task-3', title: 'Database Schema Design', description: 'Design schema for analysis sessions, thought processes, vulnerability reports, and user management', priority: 'Medium', phase: 1, assignedAgent: 'Agent-Beta', specialization: 'Backend Development', progress: 40, status: 'assigned' },
            { id: 'task-4', title: 'JSON Schema Definition', description: 'Define structured JSON schema for vulnerability reports with validation rules', priority: 'High', phase: 1, assignedAgent: 'Agent-Gamma', specialization: 'Security Analysis', progress: 100, status: 'completed' },
            // Phase 2 Tasks
            { id: 'task-5', title: 'Main Dashboard Interface', description: 'Create primary analysis interface with code input, configuration, and dual output display', priority: 'High', phase: 2, assignedAgent: '', specialization: '', progress: 0, status: 'unassigned' },
            { id: 'task-6', title: 'Real-time Progress Tracking', description: 'Implement WebSocket connections for live updates during the 7-phase analysis process', priority: 'Medium', phase: 2, assignedAgent: 'Agent-Delta', specialization: 'Frontend Development', progress: 30, status: 'assigned' },
            { id: 'task-7', title: 'Code Editor Integration', description: 'Implement syntax-highlighted code editor with multi-language support and file upload', priority: 'Medium', phase: 2, assignedAgent: '', specialization: '', progress: 0, status: 'unassigned' },
            { id: 'task-8', title: 'Dual Output Viewer', description: 'Create split-view interface for Thought-Process Log and Final Vulnerability Report', priority: 'High', phase: 2, assignedAgent: '', specialization: '', progress: 0, status: 'unassigned' },
            // Phase 3 Tasks
            { id: 'task-9', title: 'Pattern Recognition System', description: 'Implement ML-based pattern recognition to learn from previous analyses and improve accuracy', priority: 'High', phase: 3, assignedAgent: '', specialization: '', progress: 0, status: 'unassigned' },
            { id: 'task-10', title: 'Custom Security Rules Engine', description: 'Allow users to define organization-specific security rules and compliance requirements', priority: 'Medium', phase: 3, assignedAgent: '', specialization: '', progress: 0, status: 'unassigned' },
            { id: 'task-11', title: 'Git Repository Integration', description: 'Connect with Git repositories for automated security scanning of codebases', priority: 'Medium', phase: 3, assignedAgent: '', specialization: '', progress: 0, status: 'unassigned' },
            { id: 'task-12', title: 'Executive Dashboard', description: 'Create high-level security posture visualization and trend analysis for executives', priority: 'Low', phase: 3, assignedAgent: '', specialization: '', progress: 0, status: 'unassigned' },
        ];

        // Initial agents for demonstration
        activeAgents.push({ name: 'Agent-Alpha', specialization: 'AI/ML Engineering', completedTasks: 0 });
        activeAgents.push({ name: 'Agent-Beta', specialization: 'Backend Development', completedTasks: 0 });
        activeAgents.push({ name: 'Agent-Gamma', specialization: 'Security Analysis', completedTasks: 0 });
        activeAgents.push({ name: 'Agent-Delta', specialization: 'Frontend Development', completedTasks: 0 });

        function checkInAgent() {
            const agentNameInput = document.getElementById('agentName');
            const agentSpecializationSelect = document.getElementById('agentSpecialization');

            const name = agentNameInput.value.trim();
            const specialization = agentSpecializationSelect.value;

            if (name && specialization) {
                if (!activeAgents.some(agent => agent.name === name)) {
                    activeAgents.push({ name, specialization, completedTasks: 0 });
                    renderActiveAgents();
                    updateOverallStats();
                    agentNameInput.value = '';
                    agentSpecializationSelect.value = '';
                } else {
                    alert('Agent with this name already checked in!');
                }
            } else {
                alert('Please enter agent name and select specialization.');
            }
        }

        function renderActiveAgents() {
            const activeAgentsList = document.getElementById('activeAgentsList');
            if (activeAgents.length === 0) {
                activeAgentsList.innerHTML = '<p style="color: #94a3b8;">No agents currently active</p>';
            } else {
                activeAgentsList.innerHTML = activeAgents.map(agent => `
                    <span class="agent-badge">${agent.name} (${agent.specialization})</span>
                `).join('');
            }
            document.getElementById('activeAgents').textContent = activeAgents.length;
        }

        function updateOverallStats() {
            const totalTasks = tasks.length;
            const completedTasks = tasks.filter(task => task.status === 'completed').length;
            const inProgressTasks = tasks.filter(task => task.status === 'assigned' && task.progress < 100).length;
            
            document.getElementById('totalTasks').textContent = totalTasks;
            document.getElementById('completedTasks').textContent = completedTasks;
            document.getElementById('inProgressTasks').textContent = inProgressTasks;
            
            let totalProgressSum = 0;
            tasks.forEach(task => {
                totalProgressSum += task.progress;
            });
            const overallProgress = totalTasks > 0 ? (totalProgressSum / totalTasks) : 0;
            document.getElementById('overallProgress').textContent = `${overallProgress.toFixed(0)}%`;
        }

        function renderRoadmap() {
            const phase1TasksGrid = document.getElementById('phase-1-tasks');
            const phase2TasksGrid = document.getElementById('phase-2-tasks');
            const phase3TasksGrid = document.getElementById('phase-3-tasks');

            phase1TasksGrid.innerHTML = tasks.filter(t => t.phase === 1).map(task => `
                <div class="task-card ${task.status}" onclick="openTaskModal('${task.id}')">
                    <div class="task-header">
                        <div class="task-title">${task.title}</div>
                        <div class="task-priority priority-${task.priority.toLowerCase()}">${task.priority}</div>
                    </div>
                    <div class="task-description">
                        ${task.description}
                    </div>
                    <div class="task-assignment">
                        <div class="assigned-agent">
                            👤 ${task.assignedAgent || 'Unassigned'} ${task.assignedAgent ? '| ' + (activeAgents.find(a => a.name === task.assignedAgent)?.specialization || '') : ''}
                        </div>
                        <div class="progress-indicator">
                            <div class="progress-bar">
                                <div class="progress-bar-fill" style="width: ${task.progress}%"></div>
                            </div>
                            <div class="progress-text">${task.progress}%</div>
                        </div>
                    </div>
                </div>
            `).join('');

            phase2TasksGrid.innerHTML = tasks.filter(t => t.phase === 2).map(task => `
                <div class="task-card ${task.status}" onclick="openTaskModal('${task.id}')">
                    <div class="task-header">
                        <div class="task-title">${task.title}</div>
                        <div class="task-priority priority-${task.priority.toLowerCase()}">${task.priority}</div>
                    </div>
                    <div class="task-description">
                        ${task.description}
                    </div>
                    <div class="task-assignment">
                        <div class="assigned-agent">
                            👤 ${task.assignedAgent || 'Unassigned'} ${task.assignedAgent ? '| ' + (activeAgents.find(a => a.name === task.assignedAgent)?.specialization || '') : ''}
                        </div>
                        <div class="progress-indicator">
                            <div class="progress-bar">
                                <div class="progress-bar-fill" style="width: ${task.progress}%"></div>
                            </div>
                            <div class="progress-text">${task.progress}%</div>
                        </div>
                    </div>
                </div>
            `).join('');

            phase3TasksGrid.innerHTML = tasks.filter(t => t.phase === 3).map(task => `
                <div class="task-card ${task.status}" onclick="openTaskModal('${task.id}')">
                    <div class="task-header">
                        <div class="task-title">${task.title}</div>
                        <div class="task-priority priority-${task.priority.toLowerCase()}">${task.priority}</div>
                    </div>
                    <div class="task-description">
                        ${task.description}
                    </div>
                    <div class="task-assignment">
                        <div class="assigned-agent">
                            👤 ${task.assignedAgent || 'Unassigned'} ${task.assignedAgent ? '| ' + (activeAgents.find(a => a.name === task.assignedAgent)?.specialization || '') : ''}
                        </div>
                        <div class="progress-indicator">
                            <div class="progress-bar">
                                <div class="progress-bar-fill" style="width: ${task.progress}%"></div>
                            </div>
                            <div class="progress-text">${task.progress}%</div>
                        </div>
                    </div>
                </div>
            `).join('');

            updateOverallStats();
            renderActiveAgents();
        }

        function openTaskModal(taskId) {
            const task = tasks.find(t => t.id === taskId);
            if (!task) return;

            currentTaskId = taskId;
            document.getElementById('modalTaskTitle').textContent = task.title;
            document.getElementById('modalTaskDescription').textContent = task.description;

            const modalAssignAgent = document.getElementById('modalAssignAgent');
            modalAssignAgent.innerHTML = '<option value="">Select agent</option>';
            activeAgents.forEach(agent => {
                const option = document.createElement('option');
                option.value = agent.name;
                option.textContent = `${agent.name} (${agent.specialization})`;
                modalAssignAgent.appendChild(option);
            });

            modalAssignAgent.value = task.assignedAgent || '';

            const modalTaskProgress = document.getElementById('modalTaskProgress');
            modalTaskProgress.value = task.progress;
            document.getElementById('modalTaskProgressText').textContent = `${task.progress}%`;

            document.getElementById('taskModal').classList.add('active');
        }

        function closeTaskModal() {
            document.getElementById('taskModal').classList.remove('active');
            currentTaskId = null;
        }

        function saveTaskChanges() {
            const assignedAgentName = document.getElementById('modalAssignAgent').value;
            const progress = parseInt(document.getElementById('modalTaskProgress').value);

            const taskIndex = tasks.findIndex(t => t.id === currentTaskId);
            if (taskIndex === -1) return;

            tasks[taskIndex].assignedAgent = assignedAgentName;
            tasks[taskIndex].progress = progress;
            if (progress === 100) {
                tasks[taskIndex].status = 'completed';
            } else if (assignedAgentName) {
                tasks[taskIndex].status = 'assigned';
            } else {
                tasks[taskIndex].status = 'unassigned';
            }

            renderRoadmap();
            closeTaskModal();
        }

        function completeTask(taskId) {
            const taskIndex = tasks.findIndex(t => t.id === taskId);
            if (taskIndex === -1) return;

            tasks[taskIndex].status = 'completed';
            tasks[taskIndex].progress = 100;
            const assignedAgentName = tasks[taskIndex].assignedAgent;
            if (assignedAgentName) {
                const agent = activeAgents.find(a => a.name === assignedAgentName);
                if (agent) {
                    agent.completedTasks = (agent.completedTasks || 0) + 1;
                }
            }

            renderRoadmap();
        }

        // Initial render
        renderRoadmap();
    </script>
</body>
</html>