from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import json
import ollama
from typing import List, Dict, Any

from .llm_integration import get_ollama_completion, get_lmstudio_completion, METACOGNITIVE_SYSTEM_PROMPT, VulnerabilityReport

app = FastAPI()

# Configure CORS
origins = [
    "http://localhost",
    "http://localhost:3000", # Assuming your React app runs on port 3000
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class AnalyzeRequest(BaseModel):
    code: str
    context: str = ""
    analysis_type: str = "comprehensive"
    llm_model: str = "llama3"
    llm_service: str = "ollama" # "ollama" or "lmstudio"


@app.get("/health")
async def health_check():
    return {"status": "ok"}

@app.post("/analyze")
async def analyze_code(request: AnalyzeRequest):
    code = request.code
    context = request.context
    analysis_type = request.analysis_type
    llm_model = request.llm_model
    llm_service = request.llm_service

    # Simulate the Deep Reasoning Protocol phases with LLM calls
    # For a real implementation, this would involve multiple LLM calls
    # and intermediate processing based on the 7 phases.

    full_prompt = f"""
    Analyze the following code for security vulnerabilities:

    Code:
    ```\n{code}\n```

    Context: {context}

    Analysis Type: {analysis_type}

    Please follow the 7-phase Deep Reasoning Protocol and provide your thought process, followed by a final JSON vulnerability report conforming to the VulnerabilityReport Pydantic schema. The JSON report should be within <report_json> tags.
    """

    thought_process_log = ""
    final_vulnerability_report = {}

    try:
        if llm_service == "ollama":
            response = get_ollama_completion(llm_model, full_prompt, METACOGNITIVE_SYSTEM_PROMPT)
        elif llm_service == "lmstudio":
            response = get_lmstudio_completion(llm_model, full_prompt, METACOGNITIVE_SYSTEM_PROMPT)
        else:
            raise HTTPException(status_code=400, detail="Invalid LLM service specified.")

        # Extract thought process and JSON report
        # This is a simplified extraction. A more robust solution might use regex or specific parsing logic.
        response_content = response.get("message", {}).get("content", "")
        
        # This part requires the LLM to output its thought process and then the JSON in a specific tag.
        # The prompt is designed to guide the LLM to put the JSON inside <report_json> tags.
        if "<report_json>" in response_content and "</report_json>" in response_content:
            thought_process_log = response_content.split("<report_json>")[0].strip()
            json_str = response_content.split("<report_json>")[1].split("</report_json>")[0].strip()
            final_vulnerability_report = VulnerabilityReport.model_validate_json(json_str).dict()
        else:
            # If the LLM doesn't wrap JSON in tags, try to parse the whole thing
            # This is a fallback and less reliable
            try:
                final_vulnerability_report = VulnerabilityReport.model_validate_json(response_content).dict()
                thought_process_log = "No explicit thought process tags found. Attempted to parse entire response as JSON."
            except Exception as e:
                thought_process_log = response_content
                final_vulnerability_report = {"error": "Could not parse LLM output as valid JSON report.", "details": str(e)}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"LLM analysis failed: {e}")

    return {
        "thought_process_log": thought_process_log,
        "final_vulnerability_report": final_vulnerability_report
    }

@app.get("/models")
async def list_models():
    try:
        ollama_models = ollama.Client(host="http://localhost:11434").list()
        # You might also want to query LM Studio for models if applicable
        # lmstudio_models = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio").models.list()
        
        models = []
        for m in ollama_models.get("models", []):
            models.append({"name": m["name"], "service": "ollama"})
        
        return {"models": models}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve models: {e}") 