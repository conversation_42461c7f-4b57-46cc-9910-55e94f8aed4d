from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import JSONResponse
from backend.app.llm.llm_integration import LLMIntegration
from backend.app.services.deep_reasoning_protocol import DeepReasoningProtocol
from backend.app.services.rag_service import RAGService
from backend.app.models.vulnerability_report import VulnerabilityReport
from backend.app.models.code_analysis_request import CodeAnalysisRequest # New import

app = FastAPI()

# Initialize LLM and RAG services globally or using FastAPI's dependency injection
llm_integration = LLMIntegration()
rag_service = RAGService()

@app.get("/")
async def root():
    return {"message": "Sentinel Trace Backend is running!"}

@app.post("/analyze-code", response_model=VulnerabilityReport)
async def analyze_code_endpoint(request: CodeAnalysisRequest):
    """
    Analyzes the provided code for vulnerabilities using the Deep Reasoning Protocol.
    Optionally adds the analyzed code to the RAG database.
    """
    drp = DeepReasoningProtocol(llm_integration)
    
    print(f"Received analysis request for file: {request.file_path}")
    print(f"Code snippet length: {len(request.code)} characters")

    report = drp.analyze_code(request.code, request.file_path, request.llm_type)

    if report:
        # Add the analyzed code to the RAG database for future retrieval
        try:
            rag_service.add_documents(
                documents=[request.code],
                metadatas=[{"file_path": request.file_path, "analysis_summary": report.analysis_summary}],
                ids=[f"{request.file_path}_{report.timestamp}"] # Unique ID for the document
            )
            print(f"Code from {request.file_path} added to RAG database.")
        except Exception as e:
            print(f"Warning: Could not add document to RAG service: {e}")
            # Do not raise HTTPException, as core analysis is complete

        return report
    else:
        raise HTTPException(status_code=500, detail="Failed to generate a valid vulnerability report.")

@app.post("/query-rag")
async def query_rag_endpoint(query: str, n_results: int = 5):
    """
    Queries the RAG database for code snippets relevant to the given query.
    """
    print(f"Received RAG query: {query}")
    results = rag_service.query_documents(query_texts=[query], n_results=n_results)
    return JSONResponse(content=results)