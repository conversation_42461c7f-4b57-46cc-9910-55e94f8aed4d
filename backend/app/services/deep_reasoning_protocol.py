import json
from datetime import datetime
from typing import Dict, Any, Optional

from backend.app.llm.llm_integration import LLMIntegration
from backend.app.models.vulnerability_report import VulnerabilityReport, VulnerabilityDetail
from backend.app.core.system_prompts import METASEC_ANALYST_SYSTEM_PROMPT

class DeepReasoningProtocol:
    def __init__(self, llm_integration: LLMIntegration):
        self.llm = llm_integration
        self.system_prompt = METASEC_ANALYST_SYSTEM_PROMPT

    def _call_llm(self, user_prompt: str, llm_type: str = "ollama") -> str:
        """Helper to call the LLM and get a response."""
        full_prompt = f"{self.system_prompt}\n\nUser Code:\n```\n{user_prompt}\n```\n"
        return self.llm.get_llm_response(full_prompt, llm_type)

    def phase_1_initial_scan(self, code: str) -> str:
        """Phase 1: Initial Scan & Pattern Recognition."""
        prompt = f"Perform an initial scan of the following code for common vulnerable patterns and functions. Focus on quick identification. Code:\n```\n{code}\n```\n"
        response = self._call_llm(prompt)
        print(f"Phase 1 Response: {response}")
        return response

    def phase_2_contextual_analysis(self, code: str, initial_findings: str) -> str:
        """Phase 2: Contextual Analysis."""
        prompt = f"Analyze the data flow, user input points, and system interactions in the following code, considering the initial findings: {initial_findings}. Code:\n```\n{code}\n```\n"
        response = self._call_llm(prompt)
        print(f"Phase 2 Response: {response}")
        return response

    def phase_3_threat_modeling(self, code: str, contextual_analysis: str) -> str:
        """Phase 3: Threat Modeling."""
        prompt = f"Based on the code and contextual analysis ({contextual_analysis}), envision potential adversaries and their goals. How might they misuse this code? Code:\n```\n{code}\n```\n"
        response = self._call_llm(prompt)
        print(f"Phase 3 Response: {response}")
        return response

    def phase_4_vulnerability_hypothesis(self, code: str, threat_model: str) -> str:
        """Phase 4: Vulnerability Hypothesis Generation."""
        prompt = f"Formulate specific hypotheses about potential vulnerabilities in the code, considering the threat model: {threat_model}. Code:\n```\n{code}\n```\n"
        response = self._call_llm(prompt)
        print(f"Phase 4 Response: {response}")
        return response

    def phase_5_exploitation_simulation(self, code: str, hypotheses: str) -> str:
        """Phase 5: Exploitation Simulation (Mental Walkthrough)."""
        prompt = f"Step-by-step, mentally execute attack scenarios to confirm the following hypotheses: {hypotheses}. Consider edge cases and bypasses. Code:\n```\n{code}\n```\n"
        response = self._call_llm(prompt)
        print(f"Phase 5 Response: {response}")
        return response

    def phase_6_impact_assessment(self, code: str, exploitation_results: str) -> str:
        """Phase 6: Impact Assessment."""
        prompt = f"Determine the consequences of successful exploits based on the simulation results: {exploitation_results}. Code:\n```\n{code}\n```\n"
        response = self._call_llm(prompt)
        print(f"Phase 6 Response: {response}")
        return response

    def phase_7_remediation_strategy(self, code: str, impact_assessment: str) -> str:
        """Phase 7: Remediation Strategy Formulation and Report Generation."""
        prompt = f"Develop precise and effective countermeasures for the identified vulnerabilities, considering the impact assessment: {impact_assessment}. Finally, generate a structured JSON report adhering to the `VulnerabilityReport` schema for the provided code. Code:\n```\n{code}\n```\n"
        response = self._call_llm(prompt)
        print(f"Phase 7 Response: {response}")
        return response

    def analyze_code(self, code: str, file_path: str, llm_type: str = "ollama") -> Optional[VulnerabilityReport]:
        """Orchestrates the 7 phases of the Deep Reasoning Protocol."""
        print("Starting Deep Reasoning Protocol...")
        p1_result = self.phase_1_initial_scan(code)
        p2_result = self.phase_2_contextual_analysis(code, p1_result)
        p3_result = self.phase_3_threat_modeling(code, p2_result)
        p4_result = self.phase_4_vulnerability_hypothesis(code, p3_result)
        p5_result = self.phase_5_exploitation_simulation(code, p4_result)
        p6_result = self.phase_6_impact_assessment(code, p5_result)
        final_json_str = self.phase_7_remediation_strategy(code, p6_result)

        try:
            # Attempt to parse the final response as JSON
            report_data = json.loads(final_json_str)
            # Add file_path and timestamp if not already present (LLM should provide them)
            if "file_path" not in report_data:
                report_data["file_path"] = file_path
            if "timestamp" not in report_data:
                report_data["timestamp"] = datetime.now().isoformat() + "Z"

            # Validate against Pydantic model
            report = VulnerabilityReport(**report_data)
            print("Deep Reasoning Protocol completed successfully. Report generated.")
            return report
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from LLM response: {e}")
            print(f"LLM Raw Response: {final_json_str}")
            return None
        except Exception as e:
            print(f"Error validating Pydantic model: {e}")
            print(f"LLM Raw Response: {final_json_str}")
            return None

if __name__ == "__main__":
    # This is a placeholder for testing. In a real scenario, you'd run Ollama/LM Studio.
    # Ensure Ollama is running and 'llama2' model is downloaded, or LM Studio is running.
    # Example: ollama run llama2

    # Mock LLMIntegration for testing without a running LLM server
    class MockLLMIntegration(LLMIntegration):
        def __init__(self):
            super().__init__()
            self.responses = {
                "Phase 1": "Initial scan found potential input validation issues.",
                "Phase 2": "Contextual analysis shows user input directly used in SQL queries.",
                "Phase 3": "Threat: Malicious user injecting SQL commands.",
                "Phase 4": "Hypothesis: SQL Injection via username field.",
                "Phase 5": "Simulation: ' OR 1=1--' bypasses login.",
                "Phase 6": "Impact: Unauthorized access, data exfiltration.",
                "Phase 7": """
{
  "file_path": "/app/example.py",
  "vulnerabilities": [
    {
      "description": "SQL Injection vulnerability in user authentication.",
      "severity": "Critical",
      "cwe_id": "CWE-89",
      "owasp_category": "A01:2021-Broken Access Control",
      "recommendation": "Use parameterized queries or ORM (e.g., SQLAlchemy) to prevent SQL injection. Example: `cursor.execute('SELECT * FROM users WHERE username = %s', (username,))`",
      "code_snippet": "conn.execute(f'SELECT * FROM users WHERE username = \"{username}\"')",
      "line_numbers": [10, 11]
    }
  ],
  "analysis_summary": "The provided code snippet contains a critical SQL injection vulnerability in the authentication logic, allowing unauthorized access.",
  "timestamp": "2025-06-10T18:00:00Z"
}
"""
            }
            self.phase_counter = 0

        def get_llm_response(self, prompt: str, llm_type: str = "ollama") -> str:
            self.phase_counter += 1
            if self.phase_counter == 1:
                return self.responses["Phase 1"]
            elif self.phase_counter == 2:
                return self.responses["Phase 2"]
            elif self.phase_counter == 3:
                return self.responses["Phase 3"]
            elif self.phase_counter == 4:
                return self.responses["Phase 4"]
            elif self.phase_counter == 5:
                return self.responses["Phase 5"]
            elif self.phase_counter == 6:
                return self.responses["Phase 6"]
            elif self.phase_counter == 7:
                return self.responses["Phase 7"]
            return "Mock response for unknown phase."


    print("\n--- Running Deep Reasoning Protocol with Mock LLM ---")
    mock_llm = MockLLMIntegration()
    drp = DeepReasoningProtocol(mock_llm)
    sample_code = """
def authenticate_user(username, password):
    conn = get_db_connection()
    cursor = conn.cursor()
    query = f"SELECT * FROM users WHERE username = '{username}' AND password = '{password}'"
    cursor.execute(query)
    user = cursor.fetchone()
    conn.close()
    return user
"""
    report = drp.analyze_code(sample_code, "/app/auth.py", llm_type="ollama")

    if report:
        print("\nGenerated Vulnerability Report:")
        print(report.model_dump_json(indent=2))
    else:
        print("\nFailed to generate a valid vulnerability report.")