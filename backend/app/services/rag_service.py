import chromadb
from chromadb.utils import embedding_functions
from typing import List, Dict, Any

class RAGService:
    def __init__(self, collection_name: str = "code_vulnerabilities", model_name: str = "all-MiniLM-L6-v2"):
        self.client = chromadb.Client()
        self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(model_name=model_name)
        self.collection = self.client.get_or_create_collection(
            name=collection_name,
            embedding_function=self.embedding_function
        )

    def add_documents(self, documents: List[str], metadatas: List[Dict[str, Any]], ids: List[str]):
        """Adds documents (code snippets) to the ChromaDB collection."""
        if len(documents) != len(metadatas) or len(documents) != len(ids):
            raise ValueError("Documents, metadatas, and ids must have the same length.")
        self.collection.add(
            documents=documents,
            metadatas=metadatas,
            ids=ids
        )
        print(f"Added {len(documents)} documents to ChromaDB collection '{self.collection.name}'.")

    def query_documents(self, query_texts: List[str], n_results: int = 5) -> Dict[str, Any]:
        """Queries the ChromaDB collection for relevant documents."""
        results = self.collection.query(
            query_texts=query_texts,
            n_results=n_results,
            include=['documents', 'distances', 'metadatas']
        )
        return results

    def delete_collection(self):
        """Deletes the entire collection."""
        self.client.delete_collection(name=self.collection.name)
        print(f"Collection '{self.collection.name}' deleted.")

if __name__ == "__main__":
    # Example Usage
    rag_service = RAGService()

    # Clean up previous runs for fresh start
    try:
        rag_service.delete_collection()
    except Exception:
        pass # Collection might not exist yet

    # Re-initialize after potential deletion
    rag_service = RAGService()

    # 1. Add some sample code snippets as documents
    sample_codes = [
        """
def get_user_data(user_id):
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    query = f"SELECT * FROM users WHERE id = {user_id}"
    cursor.execute(query)
    data = cursor.fetchone()
    conn.close()
    return data
""",
        """
function sanitizeInput(input) {
    return input.replace(/</g, "<").replace(/>/g, ">");
}
""",
        """
import os
def read_file_unsafe(filename):
    with open(filename, 'r') as f:
        return f.read()
""",
        """
from flask import Flask, request
app = Flask(__name__)
@app.route('/login', methods=['POST'])
def login():
    username = request.form['username']
    password = request.form['password']
    # Insecure comparison
    if username == 'admin' and password == 'password123':
        return "Login successful"
    return "Invalid credentials"
"""
    ]
    sample_metadatas = [
        {"source": "auth.py", "vulnerability_type": "SQL Injection"},
        {"source": "frontend.js", "vulnerability_type": "XSS Prevention"},
        {"source": "file_ops.py", "vulnerability_type": "Path Traversal"},
        {"source": "auth.py", "vulnerability_type": "Hardcoded Credentials"}
    ]
    sample_ids = ["doc1", "doc2", "doc3", "doc4"]

    rag_service.add_documents(sample_codes, sample_metadatas, sample_ids)

    # 2. Query for relevant code snippets
    query = "How to prevent SQL injection?"
    print(f"\nQuerying for: '{query}'")
    results = rag_service.query_documents(query_texts=[query], n_results=2)

    print("\nQuery Results:")
    for i, doc in enumerate(results['documents'][0]):
        print(f"--- Result {i+1} ---")
        print(f"Document: {doc}")
        print(f"Distance: {results['distances'][0][i]}")
        print(f"Metadata: {results['metadatas'][0][i]}")
        print("-" * 20)

    query_2 = "Insecure file reading"
    print(f"\nQuerying for: '{query_2}'")
    results_2 = rag_service.query_documents(query_texts=[query_2], n_results=1)
    print("\nQuery Results:")
    for i, doc in enumerate(results_2['documents'][0]):
        print(f"--- Result {i+1} ---")
        print(f"Document: {doc}")
        print(f"Distance: {results_2['distances'][0][i]}")
        print(f"Metadata: {results_2['metadatas'][0][i]}")
        print("-" * 20)

    # Clean up
    rag_service.delete_collection()