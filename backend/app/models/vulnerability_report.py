from pydantic import BaseModel, Field
from typing import List, Optional

class VulnerabilityDetail(BaseModel):
    description: str = Field(..., description="Detailed description of the vulnerability.")
    severity: str = Field(..., description="Severity of the vulnerability (e.g., Critical, High, Medium, Low, Informational).")
    cwe_id: Optional[str] = Field(None, description="Common Weakness Enumeration (CWE) ID, if applicable.")
    owasp_category: Optional[str] = Field(None, description="OWASP Top 10 category, if applicable.")
    recommendation: str = Field(..., description="Recommended remediation steps for the vulnerability.")
    code_snippet: Optional[str] = Field(None, description="Relevant code snippet demonstrating the vulnerability.")
    line_numbers: Optional[List[int]] = Field(None, description="List of line numbers where the vulnerability is found.")

class VulnerabilityReport(BaseModel):
    file_path: str = Field(..., description="The path to the file analyzed.")
    vulnerabilities: List[VulnerabilityDetail] = Field(..., description="A list of identified vulnerabilities.")
    analysis_summary: str = Field(..., description="A summary of the code analysis and overall security posture.")
    timestamp: str = Field(..., description="Timestamp of when the analysis was performed (ISO 8601 format).")

if __name__ == "__main__":
    # Example usage and validation
    try:
        example_vulnerability = VulnerabilityDetail(
            description="SQL Injection vulnerability in user authentication.",
            severity="Critical",
            cwe_id="CWE-89",
            owasp_category="A01:2021-Broken Access Control",
            recommendation="Use parameterized queries or ORM to prevent SQL injection.",
            code_snippet="conn.execute(f'SELECT * FROM users WHERE username = \"{username}\"')",
            line_numbers=[42, 43]
        )

        example_report = VulnerabilityReport(
            file_path="/app/auth.py",
            vulnerabilities=[example_vulnerability],
            analysis_summary="The authentication module has a critical SQL injection vulnerability.",
            timestamp="2025-06-10T17:00:00Z"
        )

        print("Successfully created a valid VulnerabilityReport:")
        print(example_report.model_dump_json(indent=2))

        # Example of invalid data (missing required field)
        invalid_vulnerability_data = {
            "severity": "High",
            "recommendation": "Fix this."
        }
        # This would raise a ValidationError
        # invalid_vulnerability = VulnerabilityDetail(**invalid_vulnerability_data)

    except Exception as e:
        print(f"Error during example usage: {e}")