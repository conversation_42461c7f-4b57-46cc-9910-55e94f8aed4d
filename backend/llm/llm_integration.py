import ollama
from openai import OpenAI

class LLMIntegration:
    def __init__(self, model_name: str = "llama2"):
        self.model_name = model_name
        self.ollama_client = ollama.Client(host='http://localhost:11434')
        # Assuming LM Studio might expose an OpenAI-compatible API
        self.openai_client = OpenAI(
            base_url="http://localhost:1234/v1", # Default LM Studio port
            api_key="lm-studio" # Replace with actual key if needed
        )

    def generate_ollama_response(self, prompt: str):
        try:
            response = self.ollama_client.generate(model=self.model_name, prompt=prompt)
            return response['response']
        except Exception as e:
            return f"Error generating Ollama response: {e}"

    def generate_lm_studio_response(self, prompt: str):
        try:
            completion = self.openai_client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
            )
            return completion.choices[0].message.content
        except Exception as e:
            return f"Error generating LM Studio response: {e}"

    def get_llm_response(self, prompt: str, llm_type: str = "ollama"):
        if llm_type == "ollama":
            return self.generate_ollama_response(prompt)
        elif llm_type == "lm_studio":
            return self.generate_lm_studio_response(prompt)
        else:
            return "Invalid LLM type specified. Choose 'ollama' or 'lm_studio'."

if __name__ == "__main__":
    llm_ollama = LLMIntegration(model_name="llama2") # Ensure llama2 is downloaded in Ollama
    print("Ollama Test:")
    print(llm_ollama.get_llm_response("What is the capital of France?", "ollama"))

    llm_lm_studio = LLMIntegration(model_name="local-model") # Replace with your LM Studio model name
    print("\nLM Studio Test:")
    print(llm_lm_studio.get_llm_response("What is the capital of Germany?", "lm_studio"))