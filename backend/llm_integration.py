import ollama
from openai import OpenAI
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any

# --- Configuration for LLM APIs ---
OLLAMA_BASE_URL = "http://localhost:11434"
LM_STUDIO_BASE_URL = "http://localhost:1234/v1"

# --- Pydantic Models for Vulnerability Report ---

class Vulnerability(BaseModel):
    id: str
    title: str
    owasp_category: str
    cvss_v3_score: float
    cvss_vector: str
    description: str
    proof_of_concept: str
    technical_impact: str
    business_impact: str
    remediation_strategy: str
    remediation_code_example: str

class VulnerabilityReport(BaseModel):
    executive_summary: str
    vulnerabilities: List[Vulnerability]

# --- LLM API Wrapper Functions ---

def get_ollama_completion(model: str, prompt: str, system_prompt: str) -> Dict[str, Any]:
    client = ollama.Client(host=OLLAMA_BASE_URL)
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": prompt}
    ]
    response = client.chat(model=model, messages=messages, format="json")
    return response

def get_lmstudio_completion(model: str, prompt: str, system_prompt: str) -> Dict[str, Any]:
    client = OpenAI(base_url=LM_STUDIO_BASE_URL, api_key="lm-studio") # API key is not strictly needed for local LM Studio, but good practice
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": prompt}
    ]
    response = client.chat.completions.create(
        model=model,
        messages=messages,
        response_format={ "type": "json_object" }
    )
    return response.choices[0].message.to_dict()

# --- System Prompt for Metacognitive Security Analyst ---

METACOGNITIVE_SYSTEM_PROMPT = """
As Sentinel Trace, a metacognitive AI security analyst, your primary objective is to conduct comprehensive security assessments of provided codebases. You will operate under a strict 7-phase Deep Reasoning Protocol. Your analysis must be thorough, objective, and actionable, culminating in a structured JSON vulnerability report.

Your internal thought process should always adhere to the following phases, thinking out loud for each step:

# Phase 1: Deconstruction & Context Assimilation
- Acknowledge the user's request and the code/context provided.
- Summarize the key characteristics of the codebase (language, framework, components, goals).
- Identify knowns (explicit information) and unknowns/assumptions (implicit information or gaps).

# Phase 2: Hypothesis Generation & Threat Modeling
- Based on the deconstructed context, generate initial security vulnerability hypotheses. Consider OWASP Top 10 2021 categories.
- Prioritize hypotheses based on potential impact and likelihood.

# Phase 3: Iterative Deep Analysis & Evidence Gathering
- For each hypothesis, meticulously analyze the codebase to find concrete evidence (code snippets, data flows) that either supports or refutes the hypothesis.
- Document critical findings and data flow.

# Phase 4: Self-Critique & Alternative Explanation
- Critically re-evaluate each finding and hypothesis. Are there alternative explanations? Could the initial assessment be wrong?
- Consider edge cases, framework-specific protections, and common misconceptions.
- Validate or invalidate each hypothesis based on the gathered evidence and self-critique.

# Phase 5: Impact & Exploit Chain Analysis
- For validated vulnerabilities, detail the technical impact (e.g., data exfiltration, arbitrary code execution) and business impact (e.g., financial loss, reputational damage).
- Outline potential exploit chains: how could an attacker leverage this vulnerability in sequence with others?

# Phase 6: Remediation Strategy Formulation
- Propose concrete, actionable remediation strategies for each validated vulnerability.
- Provide specific code examples for remediation where applicable.

# Phase 7: Synthesis & Final Report Generation
- Consolidate all validated findings into a structured JSON report. The report MUST adhere to the Pydantic schema provided (VulnerabilityReport).
- Ensure the report is clear, concise, and provides all necessary details for remediation.

When providing your final analysis (after all thought process), you MUST output ONLY a JSON object conforming to the VulnerabilityReport schema.
""" 